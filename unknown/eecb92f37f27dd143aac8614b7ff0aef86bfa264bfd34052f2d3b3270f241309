import { CommonModule, formatDate } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, DropdownOption, ButtonComponent, DropdownComponent } from '@ava/play-comp-library';
import approvalText  from '../constants/approval.json';
import { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';
import { ApprovalService } from '../../../shared/services/approval.service';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';

type RequestStatus = 'approved' | 'rejected' | 'review';

@Component({
  selector: 'app-approval-tools',
  imports: [
    CommonModule,
    RouterModule,
    ApprovalCardComponent,
    IconComponent,
    AvaTextboxComponent,
    TextCardComponent,
    ReactiveFormsModule,
    AvaTagComponent,
    ButtonComponent,
    DropdownComponent,
    PopupComponent,
    ConfirmationPopupComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval-tools.component.html',
  styleUrls: ['./approval-tools.component.scss']
})

export class ApprovalToolsComponent implements OnInit {
    appLabels = approvalText.labels;

    public totalApprovedApprovals: number = 20;
    public totalPendingApprovals: number = 15;
    public totalApprovals: number = 60;
    public isBasicCollapsed: boolean = false;
    public quickActionsExpanded: boolean = true;
    public consoleApproval: any = {};
    public options: DropdownOption[] = [];
    public basicSidebarItems: any[] = [];
    public quickActions: any[] = [];
    public toolReviews: any[] = [];
    public filteredToolReviews: any[] = [];
    public workflowReviews: any[] = [];
    public agentsReviews: any[] = [];
    public currentToolsPage = 1;
    public currentAgentsPage = 1;
    public currentWorkflowsPage = 1;
    public pageSize = 50;
    public totalRecords = 0;
    public isDeleted = false;
    public currentTab = 'Tools';
    public showToolApprovalPopup = false;
    public showInfoPopup = false;
    public showErrorPopup = false;
    public infoMessage = '';
    public selectedIndex = 0;
    public showFeedbackPopup = false;
    public searchForm!: FormGroup;
    public labels: any = approvalText.labels;
    public approvedAgentId: number | null = null;
  
    constructor(
      private router: Router,
      private apiService: SharedApiServiceService,
      private approvalService: ApprovalService,
      private fb: FormBuilder,
    ) {
      this.labels = approvalText.labels;
      this.options = [
        { name: this.labels.electronics, value: 'electronics' },
        { name: this.labels.clothing, value: 'clothing' },
        { name: this.labels.books, value: 'books' },
      ];
      this.basicSidebarItems = [
        { id: '1', icon: 'hammer', text: this.labels.agents, route: '', active: true },
        { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
        { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
      ];
      this.quickActions = [
        {
          icon: 'awe_agents',
          label: this.labels.agents,
          route: '',
        },
        {
          icon: 'awe_workflows',
          label: this.labels.workflows,
          route: '',
        },
        {
          icon: 'awe_tools',
          label: this.labels.tools,
          route: '',
        },
      ];
      this.searchForm = this.fb.group({
        search: [''],
      });
    }
  
    ngOnInit(): void {
      this.searchList();
      this.totalApprovals = 60;
      this.loadToolReviews();
    }
  
    public searchList() {
      console.log(this.searchForm.get('search')?.value);
      this.searchForm
        .get('search')!
        .valueChanges.pipe(
          startWith(''),
          debounceTime(300),
          distinctUntilChanged(),
          map((value) => value?.toLowerCase() ?? ''),
        )
        .subscribe((searchText) => {
          this.applyFilter(searchText);
        });
    }
  
    public applyFilter(text: string) {
      const lower = text;
  
      if(!text){
        this.updateConsoleApproval(this.toolReviews, 'tool');
        return;
      }
  
      
      this.filteredToolReviews = this.toolReviews.filter((item) =>
          item.toolName?.toLowerCase().includes(lower),
      );

      this.updateConsoleApproval(this.filteredToolReviews, 'tool');
    }
  
    public onSelectionChange(data: any) {
      console.log('Selection changed:', data);
    }
  
    public uClick(i: any) {
      console.log('log' + i);
    }
  
    public toggleQuickActions(): void {
      this.quickActionsExpanded = !this.quickActionsExpanded;
    }
  
    public onBasicCollapseToggle(isCollapsed: boolean): void {
      this.isBasicCollapsed = isCollapsed;
      console.log('Basic sidebar collapsed:', isCollapsed);
    }
  
    public onBasicItemClick(item: any): void {
      this.basicSidebarItems.forEach((i) => (i.active = false));
      item.active = true;
      console.log(item);
    }
  
    public toRequestStatus(value: string | null | undefined): RequestStatus {
      return value === 'approved' || value === 'rejected' || value === 'review'
        ? value
        : 'review';
    }
  
    public loadToolReviews() {
      this.approvalService
        .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)
        .subscribe((response) => {
          if (this.currentToolsPage > 1) {
            this.toolReviews = [
              ...this.toolReviews,
              ...response.userToolReviewDetails,
            ];
          } else {
            this.toolReviews = response?.userToolReviewDetails;
          }
          this.toolReviews = this.toolReviews.filter(
            (r) => r.status !== 'approved',
          );
          this.filteredToolReviews = this.toolReviews;
          // console.log('tool reviews ', this.toolReviews);
          this.totalRecords = this.toolReviews.length;
          this.updateConsoleApproval(this.toolReviews, 'tool');
        });
    }
  
    public loadMoreTools(page: number) {
      this.currentToolsPage = page;
      this.loadToolReviews();
    }
  
  
    public loadReviews(name: string) {
      this.currentTab = name;
      this.loadToolReviews();
    }
  
    public rejectApproval(idx: any) {
      console.log(idx);
      // console.log(this.filteredToolReviews);
      this.selectedIndex = idx;
      this.showFeedbackPopup = true;
    }
  
    public approveApproval(idx: any) {
      console.log(idx);
      // console.log(this.filteredToolReviews);
      this.selectedIndex = idx;
      this.showToolApprovalPopup = true;
    }
  
    public handleApproval() {
      this.handleToolApproval();
    }
  
    public handleRejection(feedback: any) {
      this.handleToolRejection(feedback);
    }
  
    public handleToolApproval() {
      const toolDetails = this.filteredToolReviews[this.selectedIndex];
      const id = toolDetails.id;
      const toolId = toolDetails.toolId;
      const status = 'approved';
      const reviewedBy = toolDetails.reviewedBy;
  
      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({
        next: (response: any) => {
          this.infoMessage =
            response?.message || this.labels.toolSuccessApproveMessage;
          this.showInfoPopup = true;
        },
        error: (error) => {
          this.showErrorPopup = true;
          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
          console.error('Error:', error);
        },
      });
    }
  
    public handleToolRejection(feedback: any) {
      const toolDetails = this.filteredToolReviews[this.selectedIndex];
      const id = toolDetails.id;
      const toolId = toolDetails.toolId;
      const status = 'rejected';
      const reviewedBy = toolDetails.reviewedBy;
      const message = feedback;
      
      this.approvalService
        .rejectTool(id, toolId, status, reviewedBy, message)
        .subscribe({
          next: (response: any) => {
            this.infoMessage = response?.message || this.labels.toolSuccessRejectMessage;
            this.showInfoPopup = true;
          },
          error: (error) => {
            this.showErrorPopup = true;
            this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;
            console.error('Error:', error);
          },
        });
    }
  
    public handleInfoPopup() {
      this.showInfoPopup = false;
  
      // Check if we need to navigate to build agent screen after approval
      if (this.currentTab == 'Agents' && this.approvedAgentId) {
        // Navigate to build agent screen with the approved agent ID
        // Route structure: /build/agents/collaborative with query params
        this.router.navigate(['/build/agents/collaborative'], {
          queryParams: {
            id: this.approvedAgentId,
            mode: 'edit'
          }
        });
        // Reset the approved agent ID
        this.approvedAgentId = null;
        return;
      }
  
      this.loadToolReviews();
    }
  
    public updateConsoleApproval(data: any[], type: string) {
      this.consoleApproval = {
        contents: data?.map((req: any) => {
          const statusIcons: Record<RequestStatus, string> = {
            approved: 'circle-check-big',
            rejected: 'circle-x',
            review: 'clock',
          };
          const statusTexts: Record<RequestStatus, string> = {
            approved: this.labels.approved,
            rejected: this.labels.rejected,
            review: this.labels.review,
          };
          const statusKey = this.toRequestStatus(req?.status);
          const specificId = req.toolId;
          const title = req.toolName;
  
          return {
            id: req.id,
            refId: specificId,
            type: type,
            session1: {
              title: title,
              labels: [
                {
                  name: type,
                  color: 'success',
                  background: 'red',
                  type: 'normal',
                },
                {
                  name: req.changeRequestType,
                  color: req.changeRequestType === 'update' ? 'error' : 'info',
                  background: 'red',
                  type: 'pill',
                },
              ],
            },
            session2: [
              {
                name: type,
                color: 'default',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.status,
                color: 'default',
                background: 'red',
                type: 'normal',
              },
            ],
            session3: [
              {
                iconName: 'user',
                label: req.requestedBy,
              },
              {
                iconName: 'calendar-days',
                label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'), 
              },
            ],
            session4: {
              status: statusTexts[statusKey],
              iconName: statusIcons[statusKey],
            },
          };
        }),
        footer: {},
      };
    }
}
