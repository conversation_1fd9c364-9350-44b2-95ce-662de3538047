<div class="editor-container">
  <!-- File Explorer Section -->
  <div class="file-explorer" *ngIf="showFileExplorer">
    <!-- Enhanced search input with options -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <exp-icons
          iconName="awe_search"
          iconColor="neutralIcon"
          class="search-icon">
        </exp-icons>
        <input
          type="text"
          class="search-input"
          placeholder="Search files..."
          [(ngModel)]="searchQuery"
          (input)="filterFiles()"
          (keydown)="onFileSearchKeydown($event)" />
        <button
          *ngIf="searchQuery"
          class="clear-search-btn"
          (click)="clearFileSearch()"
          title="Clear search">
          <exp-icons iconName="awe_close" iconColor="neutralIcon"></exp-icons>
        </button>
      </div>

      <!-- Search options -->
      <div class="search-options" *ngIf="searchQuery">
        <button
          class="search-option-btn"
          [class.active]="fileSearchOptions.caseSensitive"
          (click)="toggleFileSearchOption('caseSensitive')"
          title="Match Case">
          <span class="option-text">Aa</span>
        </button>
        <button
          class="search-option-btn"
          [class.active]="fileSearchOptions.includeContent"
          (click)="toggleFileSearchOption('includeContent')"
          title="Search in file content">
          <span class="option-text">{{ '{' }} {{ '}' }}</span>
        </button>
      </div>

      <!-- Search results info -->
      <div class="search-results-info" *ngIf="searchQuery && filteredFilesCount >= 0">
        <span class="results-text">{{ getFileSearchResultsText() }}</span>
      </div>
    </div>

    <ng-template #fileTree let-files="files" let-level="level">
      <div *ngFor="let file of files" class="file-item" [class.folder]="file.type === 'folder'">
        <div class="file-row" [ngClass]="theme + '-theme'" [style.padding-left]="level * 8 + 'px'">
          <!-- Folder Display -->
          <span *ngIf="file.type === 'folder'" (click)="toggleFolder(file)" class="folder-item">
            <div class="file-box">
              <span class="arrow-icon" [class.expanded]="file.expanded">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="100%"
                  height="100%"
                  viewBox="0 -960 960 960"
                  class="chevron-icon"
                  fill="currentColor">
                  <path
                    d="M530-481 353-658q-9-9-8.5-21t9.5-21 21.5-9 21.5 9l198 198q5 5 7 10t2 11-2 11-7 10L396-261q-9 9-21 8.5t-21-9.5-9-21.5 9-21.5z"></path>
                </svg>
              </span>
              <span class="file-icon-wrapper">
                <img src="assets/icons/awe_folder.svg" alt="Folder" width="16" height="16">
              </span>
              <span class="folder-text">{{ file.name }}</span>
            </div>
          </span>
          <!-- File Display -->
          <span *ngIf="file.type === 'file'" (click)="selectFile(file)" class="file-item-name">
            <div class="file-box" [class.selected]="activeFile?.name === file.name">
              <span class="file-icon-wrapper">
                <img [src]="'assets/icons/' + getFileIcon(file.name) + '.svg'" [alt]="file.name" width="16" height="16">
              </span>
              <span class="file-text">{{ file.name }}</span>
            </div>
          </span>
        </div>
        <!-- Recursively Render Child Files/Folders -->
        <div
          *ngIf="file.type === 'folder' && file.expanded"
          class="nested-files"
          class="file-item-name">
          <ng-container
            *ngTemplateOutlet="
              fileTree;
              context: { files: file.children, level: level + 1 }
            "></ng-container>
        </div>
      </div>
    </ng-template>
    <ng-container *ngTemplateOutlet="fileTree; context: { files: files, level: 0 }"></ng-container>
  </div>

  <!-- Monaco Editor Section -->
  <div class="editor-wrapper">

    <!-- Tab Header -->
    <div class="tab-header" *ngIf="activeFiles.length > 0">
      <div class="tab-list" role="tablist" aria-orientation="horizontal">
        <div
          *ngFor="let file of activeFiles"
          class="tab-item"
          role="tab"
          [class.active]="activeFile?.name === file.name"
          (click)="setActiveFile(file)">
          <span>{{ file.name }}</span>

          <button class="close-button" (click)="closeTab(file); $event.stopPropagation()">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              viewBox="0 -960 960 960"
              class="close-icon"
              fill="currentColor">
              <path
                d="M480-438 270-228q-9 9-21 9t-21-9-9-21 9-21l210-210-210-210q-9-9-9-21t9-21 21-9 21 9l210 210 210-210q9-9 21-9t21 9 9 21-9 21L522-480l210 210q9 9 9 21t-9 21-21 9-21-9z"></path>
            </svg>
          </button>
          <!-- <awe-icons
            size="small"
            class="close-button"
            iconName="awe_close"
            (click)="closeTab(file); $event.stopPropagation()"
            role="button"></awe-icons> -->
        </div>
      </div>
    </div>

    <!-- <div *ngIf="isLoading" class="loading-indicator">Loading files...</div> -->
    <!-- Monaco Editor Loading States -->
    @if (isMonacoLoading || monacoLoadingState === 'loading') {
      <app-monaco-loader></app-monaco-loader>
    } @else if (monacoLoadingState === 'error' && monacoError) {
      <!-- Fallback error display if monaco-loader fails -->
      <div class="monaco-error-indicator">
        <div class="error-content">
          <div class="error-icon">⚠️</div>
          <h3>Failed to Load Code Editor</h3>
          <p>{{ monacoError }}</p>
          <button class="retry-button" (click)="initializeEditor()">
            Retry Loading
          </button>
        </div>
      </div>
    }

    <!-- Welcome Message -->
    <div *ngIf="showWelcomeMessage && !isLoading && !isMonacoLoading && monacoLoadingState !== 'error'" class="welcome-message">
      <div class="welcome-content">
        <h3>Code Viewer Ready</h3>
        <p>Select a file from the explorer to view its contents</p>
      </div>
    </div>

    <!-- Monaco Editor Container -->
    <div #editorContainer
         class="monaco-editor-container"
         [class.hidden]="isMonacoLoading || monacoLoadingState === 'error'"></div>
  </div>
</div>

