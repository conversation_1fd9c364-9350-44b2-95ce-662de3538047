import { Configuration } from '@azure/msal-browser';

// Feature flag to enable/disable authentication
export const AUTH_ENABLED = false; // Set this to true to re-enable authentication

// MSAL configuration
export const msalConfig: Configuration = {
  auth: {
    clientId: '6e72dbc1-ac10-43f2-9568-02cae273737a', // Your client ID
    authority: 'https://login.microsoftonline.com/d7758e8f-1df3-489f-86b5-a2254f55f9cc', // Your authority
    redirectUri: 'http://kind-island-0ff6ddd0f.6.azurestaticapps.net/experience/main/', // Your redirect URI
    postLogoutRedirectUri: 'http://kind-island-0ff6ddd0f.6.azurestaticapps.net/experience/main/', // Your post logout redirect URI
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case 0:

            return;
          case 1:

            return;
          case 2:

            return;
          case 3:

            return;
          default:
            return;
        }
      },
      logLevel: 3,
    },
  },
};

// Mock user profile for when auth is disabled
export const mockUserProfile = {
  id: 'mock-user-id',
  displayName: 'Mock User',
  givenName: 'Mock',
  surname: 'User',
  userPrincipalName: '<EMAIL>',
  mail: '<EMAIL>'
};

// Add here scopes for id token to be used at MS Identity Platform endpoints.
export const loginRequest = {
  scopes: ['User.Read', 'profile', 'openid', 'email']
};

// Add here the endpoints for MS Graph API services you'd like to use.
export const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me'
};

// Add here scopes for access token to be used at the API endpoints.
export const protectedResources = {
  apiExperienceStudio: {
    endpoint: 'https://ava-plus-experience-studio-api-dev.azurewebsites.net',
    scopes: ['api://6e72dbc1-ac10-43f2-9568-02cae273737a/access_as_user'],
  },
};